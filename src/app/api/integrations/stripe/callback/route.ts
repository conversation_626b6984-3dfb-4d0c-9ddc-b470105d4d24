import { clerkClient } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { upsertStripeIntegration } from '../../../../../lib/integrations';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Handle OAuth errors
    if (error) {
      console.error('Stripe OAuth error:', error, errorDescription);
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', error);
      redirectUrl.searchParams.set('error_description', errorDescription || '');
      return NextResponse.redirect(redirectUrl);
    }

    // Validate required parameters
    if (!code || !state) {
      console.error('Missing code or state parameter');
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'invalid_request');
      redirectUrl.searchParams.set(
        'error_description',
        'Missing required parameters'
      );
      return NextResponse.redirect(redirectUrl);
    }

    // Validate environment variables
    const clientSecret = process.env.STRIPE_SECRET_KEY;
    if (!clientSecret) {
      console.error('STRIPE_SECRET_KEY environment variable is not set');
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'server_error');
      redirectUrl.searchParams.set(
        'error_description',
        'Server configuration error'
      );
      return NextResponse.redirect(redirectUrl);
    }

    // Decode and validate state
    let stateData;
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64url').toString());
    } catch (err) {
      console.error('Invalid state parameter:', err);
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'invalid_state');
      redirectUrl.searchParams.set(
        'error_description',
        'Invalid state parameter'
      );
      return NextResponse.redirect(redirectUrl);
    }

    const { orgId, userId, environment, timestamp } = stateData;

    // Validate state data
    if (!orgId || !userId || !environment) {
      console.error('Invalid state data:', stateData);
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'invalid_state');
      redirectUrl.searchParams.set('error_description', 'Invalid state data');
      return NextResponse.redirect(redirectUrl);
    }

    // Check if state is not too old (1 hour max)
    if (Date.now() - timestamp > 60 * 60 * 1000) {
      console.error('State parameter expired');
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'expired_state');
      redirectUrl.searchParams.set('error_description', 'OAuth state expired');
      return NextResponse.redirect(redirectUrl);
    }

    // Initialize Stripe
    const stripe = new Stripe(clientSecret, {
      apiVersion: '2025-08-27.basil',
    });

    // Exchange authorization code for access token
    const tokenResponse = await stripe.oauth.token({
      grant_type: 'authorization_code',
      code,
    });

    console.log(tokenResponse, 'TOKEN RESPONSE here');

    // Validate required token response fields
    if (
      !tokenResponse.stripe_user_id ||
      !tokenResponse.access_token ||
      tokenResponse.livemode === undefined ||
      !tokenResponse.scope ||
      !tokenResponse.stripe_publishable_key
    ) {
      console.error(
        'Missing required fields in token response:',
        tokenResponse
      );
      const redirectUrl = new URL('/integrate', request.url);
      redirectUrl.searchParams.set('error', 'invalid_response');
      redirectUrl.searchParams.set(
        'error_description',
        'Invalid response from Stripe'
      );
      return NextResponse.redirect(redirectUrl);
    }

    console.log('UPSERTING:', {
      orgId,
      environment: tokenResponse,
      externalId: tokenResponse.stripe_user_id,
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token || undefined,
      livemode: tokenResponse.livemode,
      scope: tokenResponse.scope,
      stripeUserId: tokenResponse.stripe_user_id,
      stripePublishableKey: tokenResponse.stripe_publishable_key,
    });

    // Store the integration in the database
    await upsertStripeIntegration({
      orgId,
      environment: environment as 'development' | 'production',
      externalId: tokenResponse.stripe_user_id,
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token || undefined,
      livemode: tokenResponse.livemode,
      scope: tokenResponse.scope,
      stripeUserId: tokenResponse.stripe_user_id,
      stripePublishableKey: tokenResponse.stripe_publishable_key,
    });

    // update clerk organization with metadata about hasStripeIntegration: true
    const cc = await clerkClient();
    cc.organizations.updateOrganization(orgId, {
      publicMetadata: {
        hasIntegration: true,
        stripeCustomerId: tokenResponse.stripe_user_id,
      },
    });

    // Redirect to success page
    const redirectUrl = new URL('/redirect', request.url);
    redirectUrl.searchParams.set('message', 'You integrated Stripe!');

    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error('Error processing Stripe OAuth callback:', error);

    // Redirect to error page
    const redirectUrl = new URL('/integrate', request.url);
    redirectUrl.searchParams.set('error', 'server_error');
    redirectUrl.searchParams.set(
      'error_description',
      'Failed to process OAuth callback'
    );

    return NextResponse.redirect(redirectUrl);
  }
}
