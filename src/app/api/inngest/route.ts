// this is ingest route created using ingest serve

import { serve } from 'inngest/next';
import { inngest } from '../../../ingest/client';
import { helloWorld, ingestTestData } from '../../../ingest/functions';

// // Create an API that serves zero functions
export const { GET, POST, PUT } = serve({
  client: inngest,
  functions: [helloWorld, ingestTestData],
});

// export const dynamic = 'force-dynamic';
