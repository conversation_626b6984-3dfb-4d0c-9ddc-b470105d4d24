import { auth } from '@clerk/nextjs/server';
import { ReactNode } from 'react';
import { Sidebar, TopHeader } from '../../components';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default async function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  await auth.protect();
  // chek also if user has any orgs, and if user has any integrations, if no orgs, redirect to onboarding, if no integrations, redirect to integrate

  return (
    <div className='min-h-screen bg-gray-50 flex'>
      {/* Sidebar */}
      <Sidebar />

      {/* Main content area */}
      <div className='flex-1 flex flex-col'>
        {/* Top header */}
        <TopHeader />

        {/* Page content */}
        <main className='flex-1 p-6'>{children}</main>
      </div>
    </div>
  );
}
