import Stripe from 'stripe';
import {
  getAccessTokenFromIntegration,
  getIntegrationById,
} from '../lib/integrations';
import { inngest } from './client';

export const helloWorld = inngest.createFunction(
  { id: 'hello-world' },
  { event: 'test/hello.world' },
  async ({ event, step }) => {
    await step.sleep('wait-a-moment', '1s');
    return { message: `Hello ${event.data.email}!` };
  }
);

interface IngestTestDataParams {
  orgIntegrationId: string;
  numberOfInvoices: number;
  numberOfCustomers: number;
  periodMonths: number;
}

// SaaS product templates
const STANDALONE_PRODUCTS = [
  { name: 'Premium Analytics Report', price: 4900 }, // $49.00
  { name: 'Custom Dashboard Setup', price: 12900 }, // $129.00
  { name: 'Data Migration Service', price: 29900 }, // $299.00
  { name: 'Advanced Integration Package', price: 19900 }, // $199.00
  { name: 'Priority Support Session', price: 9900 }, // $99.00
];

const SUBSCRIPTION_PRODUCTS = [
  { name: 'Starter Plan', price: 2900, interval: 'month' }, // $29.00/month
  { name: 'Professional Plan', price: 7900, interval: 'month' }, // $79.00/month
  { name: 'Enterprise Plan', price: 19900, interval: 'month' }, // $199.00/month
  { name: 'Team Plan', price: 14900, interval: 'month' }, // $149.00/month
  { name: 'Premium Plan', price: 39900, interval: 'month' }, // $399.00/month
];

export const ingestTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/ingest.test.data' },
  async ({ event, step }) => {
    const {
      orgIntegrationId,
      numberOfInvoices,
      numberOfCustomers,
      periodMonths,
    } = event.data as IngestTestDataParams;

    // Step 1: Get and validate integration
    const integration = await step.run('get-integration', async () => {
      const integration = await getIntegrationById(orgIntegrationId);
      if (!integration) {
        throw new Error(`Integration with ID ${orgIntegrationId} not found`);
      }

      // SAFETY CHECK: Ensure this is NOT a live integration
      if (integration.settings?.livemode === true) {
        throw new Error(
          'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
        );
      }

      if (integration.environment === 'production') {
        throw new Error(
          'SAFETY ERROR: Cannot populate test data on a production environment integration!'
        );
      }

      return integration;
    });

    // Step 2: Initialize Stripe client
    const stripe = await step.run('initialize-stripe', async () => {
      const accessToken = getAccessTokenFromIntegration(integration);
      if (!accessToken) {
        throw new Error('No access token found for integration');
      }

      return new Stripe(accessToken, {
        apiVersion: '2025-08-27.basil',
      });
    });

    // Step 3: Create products
    const products = await step.run('create-products', async () => {
      const createdProducts = [];

      // Create standalone products
      for (const product of STANDALONE_PRODUCTS) {
        const stripeProduct = await stripe.products.create({
          name: product.name,
          type: 'service',
          metadata: { type: 'standalone' },
        });

        const price = await stripe.prices.create({
          product: stripeProduct.id,
          unit_amount: product.price,
          currency: 'usd',
        });

        createdProducts.push({
          product: stripeProduct,
          price,
          type: 'standalone',
          amount: product.price,
        });
      }

      // Create subscription products
      for (const product of SUBSCRIPTION_PRODUCTS) {
        const stripeProduct = await stripe.products.create({
          name: product.name,
          type: 'service',
          metadata: { type: 'subscription' },
        });

        const price = await stripe.prices.create({
          product: stripeProduct.id,
          unit_amount: product.price,
          currency: 'usd',
          recurring: { interval: product.interval as 'month' },
        });

        createdProducts.push({
          product: stripeProduct,
          price,
          type: 'subscription',
          amount: product.price,
        });
      }

      return createdProducts;
    });

    // Step 4: Create customers
    const customers = await step.run('create-customers', async () => {
      const createdCustomers = [];

      for (let i = 0; i < numberOfCustomers; i++) {
        const customer = await stripe.customers.create({
          email: `test-customer-${i + 1}@example.com`,
          name: `Test Customer ${i + 1}`,
          description: `Generated test customer ${i + 1}`,
        });
        createdCustomers.push(customer);
      }

      return createdCustomers;
    });

    // Step 5: Generate invoices with growth pattern
    const invoices = await step.run('generate-invoices', async () => {
      const createdInvoices = [];
      const baseRevenue = 10000; // Starting monthly revenue in cents ($100)
      const growthRate = 0.02 + Math.random() * 0.03; // 2-5% monthly growth

      for (let month = 0; month < periodMonths; month++) {
        const monthlyRevenue = Math.floor(
          baseRevenue * Math.pow(1 + growthRate, month)
        );
        const invoicesThisMonth = Math.floor(numberOfInvoices / periodMonths);

        // Calculate date for this month (going backwards from now)
        const invoiceDate = new Date();
        invoiceDate.setMonth(
          invoiceDate.getMonth() - (periodMonths - month - 1)
        );

        for (let i = 0; i < invoicesThisMonth; i++) {
          // Randomly choose between standalone and subscription (50/50 split)
          const isSubscription = Math.random() < 0.5;
          const productPool = isSubscription
            ? products.filter(p => p.type === 'subscription')
            : products.filter(p => p.type === 'standalone');

          const selectedProduct =
            productPool[Math.floor(Math.random() * productPool.length)];
          const customer =
            customers[Math.floor(Math.random() * customers.length)];

          // Create invoice
          const invoice = await stripe.invoices.create({
            customer: customer.id,
            collection_method: 'charge_automatically',
            auto_advance: true,
            metadata: {
              generated: 'true',
              month: month.toString(),
              type: selectedProduct.type,
            },
          });

          // Add line item
          await stripe.invoiceItems.create({
            customer: customer.id,
            invoice: invoice.id,
            price: selectedProduct.price.id,
            quantity: 1,
          });

          // Finalize and pay the invoice
          const finalizedInvoice = await stripe.invoices.finalizeInvoice(
            invoice.id
          );

          // Mark as paid (simulate successful payment)
          await stripe.invoices.pay(finalizedInvoice.id, {
            source: 'tok_visa', // Test token
          });

          createdInvoices.push(finalizedInvoice);
        }
      }

      return createdInvoices;
    });

    return {
      success: true,
      message: `Successfully created test data for integration ${orgIntegrationId}`,
      summary: {
        integration: integration.id,
        environment: integration.environment,
        livemode: integration.settings?.livemode,
        productsCreated: products.length,
        customersCreated: customers.length,
        invoicesCreated: invoices.length,
        periodMonths,
        totalRevenue: invoices.reduce(
          (sum, inv) => sum + (inv.amount_paid || 0),
          0
        ),
      },
    };
  }
);
