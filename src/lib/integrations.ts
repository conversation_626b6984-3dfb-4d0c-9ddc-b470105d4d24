import { clerkClient } from '@clerk/nextjs/server';
import { and, eq } from 'drizzle-orm';
import { db } from '../db/client';
import { orgIntegrations } from '../db/schema';

export interface StripeIntegrationData {
  orgId: string;
  environment: 'development' | 'production';
  externalId: string; // Stripe account ID (acct_xxx)
  accessToken: string;
  refreshToken?: string;
  livemode: boolean;
  scope: string;
  stripeUserId: string;
  stripePublishableKey: string;
}

export interface IntegrationSettings extends Record<string, unknown> {
  accessToken: string;
  refreshToken?: string;
  livemode: boolean;
  scope: string;
  stripeUserId: string;
  stripePublishableKey: string;
  secrets: {
    accessToken: string;
    refreshToken?: string;
  };
}

/**
 * Create or update a Stripe integration for an organization
 */
export async function upsertStripeIntegration(data: StripeIntegrationData) {
  const settings: IntegrationSettings = {
    accessToken: data.accessToken,
    refreshToken: data.refreshToken,
    livemode: data.livemode,
    scope: data.scope,
    stripeUserId: data.stripeUserId,
    stripePublishableKey: data.stripePublishableKey,
    secrets: {
      accessToken: data.accessToken,
      refreshToken: data.refreshToken,
    },
  };

  // Check if integration already exists
  const existing = await db
    .select()
    .from(orgIntegrations)
    .where(
      and(
        eq(orgIntegrations.orgId, data.orgId),
        eq(orgIntegrations.provider, 'stripe'),
        eq(orgIntegrations.environment, data.environment)
      )
    )
    .limit(1);

  if (existing.length > 0) {
    // Update existing integration
    const [updated] = await db
      .update(orgIntegrations)
      .set({
        externalId: data.externalId,
        settings,
        secretRef: '', // Keep empty as requested
        status: 'connected',
        connectedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(orgIntegrations.id, existing[0].id))
      .returning();

    console.log(updated, 'UPDATED', existing);

    return updated;
  } else {
    console.log('CREATING:', {
      orgId: data.orgId,
      environment: data.environment,
      externalId: data.externalId,
      accessToken: data.accessToken,
      refreshToken: data.refreshToken,
      livemode: data.livemode,
      scope: data.scope,
      stripeUserId: data.stripeUserId,
      stripePublishableKey: data.stripePublishableKey,
    });
    // Create new integration
    const [created] = await db
      .insert(orgIntegrations)
      .values({
        orgId: data.orgId,
        provider: 'stripe',
        environment: data.environment,
        externalId: data.externalId,
        settings,
        secretRef: '', // Keep empty as requested
        status: 'connected',
        connectedAt: new Date(),
      })
      .returning();

    console.log(created, 'THHRHRHR');

    return created;
  }
}

/**
 * Get Stripe integration for an organization
 */
export async function getStripeIntegration(
  orgId: string,
  environment: 'development' | 'production' = 'production'
) {
  const [integration] = await db
    .select()
    .from(orgIntegrations)
    .where(
      and(
        eq(orgIntegrations.orgId, orgId),
        eq(orgIntegrations.provider, 'stripe'),
        eq(orgIntegrations.environment, environment)
      )
    )
    .limit(1);

  return integration || null;
}

/**
 * Get all integrations for an organization
 */
export async function getOrgIntegrations(orgId: string) {
  return await db
    .select()
    .from(orgIntegrations)
    .where(eq(orgIntegrations.orgId, orgId));
}

export async function hasActiveOrgIntegration(orgId: string) {
  const cc = await clerkClient();
  const organization = await cc.organizations.getOrganization({
    organizationId: orgId,
  });

  return !!organization.publicMetadata?.hasIntegration;
}

/**
 * Delete a Stripe integration
 */
export async function deleteStripeIntegration(
  orgId: string,
  environment: 'development' | 'production' = 'production'
) {
  const [deleted] = await db
    .update(orgIntegrations)
    .set({
      status: 'revoked',
      revokedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(orgIntegrations.orgId, orgId),
        eq(orgIntegrations.provider, 'stripe'),
        eq(orgIntegrations.environment, environment)
      )
    )
    .returning();

  return deleted || null;
}

/**
 * Extract access token from integration settings
 */
export function getAccessTokenFromIntegration(integration: any): string | null {
  if (!integration?.settings?.secrets?.accessToken) {
    return null;
  }
  return integration.settings.secrets.accessToken;
}

/**
 * Get integration by ID
 */
export async function getIntegrationById(integrationId: string) {
  const [integration] = await db
    .select()
    .from(orgIntegrations)
    .where(eq(orgIntegrations.id, integrationId))
    .limit(1);

  return integration || null;
}
